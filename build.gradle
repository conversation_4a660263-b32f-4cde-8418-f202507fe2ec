plugins {
    id 'java'
}

sourceCompatibility = 17
targetCompatibility = 17

repositories {
    mavenCentral()
}

sourceSets {
    main {
        java {
            srcDir './src/main/java'
        }
    }
}

dependencies {
    implementation 'net.portswigger.burp.extensions:montoya-api:2023.12.1'
    implementation 'com.google.guava:guava:32.1.3-jre'
    implementation 'org.jsoup:jsoup:1.17.1'
    implementation 'com.google.code.gson:gson:2.10.1'
    implementation 'org.xerial:sqlite-jdbc:********'
}

test {
    useJUnitPlatform()
}

jar {
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE

    from {
        configurations.runtimeClasspath.collect { it.isDirectory() ? it : zipTree(it) }
    }
}